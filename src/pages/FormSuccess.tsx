import React from 'react';
import { CheckCircle, Phone, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

const FormSuccess = () => {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-success/10 rounded-full mb-6">
            <CheckCircle className="w-10 h-10 text-success" />
          </div>
          
          <h1 className="text-3xl font-bold title-readable mb-4">
            Thank You!
          </h1>
          
          <p className="text-lg text-muted-foreground mb-6">
            Your consultation request has been submitted successfully. We'll contact you within 2 hours during business hours to schedule your free consultation.
          </p>
          
          <div className="bg-muted rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 mb-2">
              <Phone className="w-4 h-4 text-accent" />
              <span className="font-semibold">Need immediate assistance?</span>
            </div>
            <a 
              href="tel:+***********" 
              className="text-accent hover:text-accent/80 font-bold"
            >
              Call (*************
            </a>
          </div>
        </div>
        
        <div className="space-y-3">
          <Button 
            onClick={() => window.location.href = '/'}
            className="w-full btn-hero"
          >
            <Home className="w-4 h-4 mr-2" />
            Return to Home
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FormSuccess;
