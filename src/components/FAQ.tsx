
import React from 'react';
import { Phone } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { trackPhoneClick } from '@/lib/analytics';

const FAQ = () => {
  const faqs = [
    {
      question: "How much does wood floor refinishing cost?",
      answer: "Our refinishing services typically cost $3-$4 per square foot, depending on the condition of your floors and the type of finish you choose. This includes sanding, staining (if desired), and applying a protective finish. We provide free, detailed estimates so you know exactly what to expect."
    },
    {
      question: "How long does the refinishing process take?",
      answer: "Most refinishing projects take 2-4 days to complete. This includes sanding, staining, and applying multiple coats of finish with proper drying time between each coat. We'll provide a detailed timeline during your free consultation based on your specific project."
    },
    {
      question: "Do I need to move out during refinishing?",
      answer: "You don't typically need to move out, but you'll need to stay off the floors during the process. We use a dustless sanding system to minimize mess, and we'll work with you to plan around your schedule. The floors are usually walkable within 24 hours and ready for furniture in 2-3 days."
    },
    {
      question: "Can damaged or water-damaged floors be refinished?",
      answer: "In many cases, yes! We specialize in restoring damaged floors, including water damage, pet stains, scratches, and gouges. During our free assessment, we'll evaluate your floors and let you know if refinishing is possible or if board replacement is needed."
    },
    {
      question: "What's included in your free consultation?",
      answer: "Our free consultation includes a thorough assessment of your floors, discussion of your goals and timeline, explanation of the refinishing process, and a detailed written estimate. We'll also answer all your questions and provide maintenance recommendations."
    },
    {
      question: "Do you offer any warranties or guarantees?",
      answer: "Yes! We stand behind our work with a comprehensive warranty on all refinishing projects. We're also fully licensed and insured for your protection. If you're not completely satisfied with the results, we'll work with you to make it right."
    },
    {
      question: "What areas do you serve?",
      answer: "We serve the greater Denver area including Denver, Aurora, Lakewood, Thornton, Westminster, Arvada, Centennial, Boulder, Broomfield, Highlands Ranch, and surrounding communities. There's no additional charge for travel within our service area."
    },
    {
      question: "How do I maintain my newly refinished floors?",
      answer: "We provide detailed maintenance instructions with every project. Generally, this includes regular sweeping/vacuuming, using appropriate cleaning products, controlling humidity levels, and using furniture pads. Proper maintenance will keep your floors beautiful for years to come."
    }
  ];

  return (
    <section className="bg-background section-padding">
      <div className="container mx-auto container-padding">
        <div className="section-header scroll-reveal">
          <h2 className="section-title">
            Frequently Asked Questions
          </h2>
          <p className="section-description">
            Got questions? We've got answers. Here are the most common questions we hear from Denver homeowners.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="card-premium">
                <AccordionTrigger className="px-6 py-4 text-left hover:no-underline">
                  <span className="font-semibold title-readable pr-4">{faq.question}</span>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 text-muted-foreground leading-relaxed">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          {/* Still Have Questions */}
          <div className="text-center mt-16 p-10 bg-muted rounded-3xl scroll-reveal-scale">
            <h3 className="text-3xl font-bold mb-6 title-readable">
              Still Have Questions?
            </h3>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              Don't see your question answered here? Give us a call or schedule your free consultation
              and we'll be happy to address any concerns you have.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:+17206019464"
                className="inline-flex items-center justify-center btn-primary px-6 py-3"
                onClick={() => trackPhoneClick('(*************', 'faq')}
              >
                <Phone className="w-5 h-5 mr-2" />
                Call (*************
              </a>
              <button 
                onClick={() => {
                  const element = document.getElementById('contact');
                  element?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="inline-flex items-center justify-center btn-hero px-6 py-3"
              >
                Schedule Free Consultation
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
