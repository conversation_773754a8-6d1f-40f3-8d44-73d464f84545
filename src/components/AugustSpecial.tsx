
import React from 'react';
import { Calendar, Gift, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { trackPhoneClick, trackSpecialOfferInteraction } from '@/lib/analytics';

const AugustSpecial = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="bg-accent text-accent-foreground -mt-16 pt-24 pb-16 rounded-t-4xl rounded-b-4xl">
      <div className="container mx-auto container-padding">
        <div className="text-center max-w-4xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <Gift className="w-5 h-5" />
            <span className="font-bold">LIMITED TIME OFFER</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            August Special: Save $250
          </h2>
          
          <p className="text-xl md:text-2xl mb-8 opacity-90">
            Get $250 OFF your wood floor refinishing project 
            <span className="font-bold"> + FREE in-home consultation</span>
          </p>

          {/* Offer Details */}
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="text-3xl font-bold mb-2">$250</div>
              <div className="font-medium">OFF Refinishing</div>
              <div className="text-sm opacity-80 mt-2">Minimum 500 sq ft</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="text-3xl font-bold mb-2">FREE</div>
              <div className="font-medium">Consultation</div>
              <div className="text-sm opacity-80 mt-2">In-home estimate</div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <div className="text-3xl font-bold mb-2">$3-4</div>
              <div className="font-medium">Per Sq Ft</div>
              <div className="text-sm opacity-80 mt-2">Affordable pricing</div>
            </div>
          </div>

          {/* Urgency */}
          <div className="flex items-center justify-center gap-2 mb-6">
            <Calendar className="w-5 h-5" />
            <span className="font-semibold">Offer expires August 31st, 2024</span>
          </div>

          {/* CTA */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg"
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-bold px-8 py-4 text-lg"
              onClick={scrollToContact}
            >
              <Gift className="w-5 h-5 mr-2" />
              Claim My $250 Savings
            </Button>
            <Button 
              variant="outline"
              size="lg"
              className="bg-white/10 border-white/30 hover:bg-white/20 backdrop-blur-sm font-semibold px-8 py-4 text-lg"
              onClick={() => {
                trackSpecialOfferInteraction('August $250 OFF Special', 'claim');
                trackPhoneClick('(*************', 'august-special');
                window.open('tel:+17206019464');
              }}
            >
              <Phone className="w-5 h-5 mr-2" />
              Call Now: (*************
            </Button>
          </div>

          {/* Fine Print */}
          <p className="text-sm opacity-70 mt-6">
            *Valid for new customers only. Cannot be combined with other offers. 
            Minimum 500 sq ft. Expires 8/31/24.
          </p>
        </div>
      </div>
    </section>
  );
};

export default AugustSpecial;
