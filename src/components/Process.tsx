
import React from 'react';
import { Calendar, Calculator, Sparkles, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useScrollReveal } from '@/hooks/useScrollReveal';

const Process = () => {
  const sectionRef = useScrollReveal();

  const steps = [
    {
      step: 1,
      icon: Calendar,
      title: "Free Consultation",
      description: "Schedule your free in-home consultation. We'll assess your floors, discuss your vision, and answer all your questions.",
      duration: "30-45 minutes",
      details: [
        "Floor condition assessment",
        "Custom recommendations",
        "Timeline discussion",
        "Answer your questions"
      ]
    },
    {
      step: 2,
      icon: Calculator,
      title: "Detailed Estimate",
      description: "Receive a comprehensive, no-pressure estimate with transparent pricing and project timeline.",
      duration: "Same day",
      details: [
        "Detailed project scope",
        "Transparent pricing",
        "Material recommendations",
        "Timeline and scheduling"
      ]
    },
    {
      step: 3,
      icon: Sparkles,
      title: "Beautiful Transformation",
      description: "Our expert craftsmen transform your floors with precision, care, and attention to every detail.",
      duration: "2-4 days",
      details: [
        "Professional preparation",
        "Expert craftsmanship",
        "Quality materials",
        "Final inspection"
      ]
    }
  ];

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section ref={sectionRef} className="bg-background section-padding">
      <div className="container mx-auto container-padding">
        <div className="section-header scroll-reveal">
          <h2 className="section-title">
            Simple 3-Step Process
          </h2>
          <p className="section-description">
            From your first call to the final reveal, we make wood floor refinishing easy and stress-free.
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isLast = index === steps.length - 1;
            
            return (
              <div key={index} className="relative">
                <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
                  {/* Content */}
                  <div className={`${index % 2 === 1 ? 'lg:order-2' : ''} scroll-reveal-left stagger-${index + 1}`}>
                    <div className="flex items-center gap-4 mb-6">
                      <div className="flex items-center justify-center w-16 h-16 bg-primary rounded-full text-primary-foreground font-bold text-xl">
                        {step.step}
                      </div>
                      <div className="flex items-center justify-center w-12 h-12 bg-accent/10 rounded-full">
                        <Icon className="w-6 h-6 text-accent" />
                      </div>
                    </div>
                    
                    <h3 className="text-3xl font-bold mb-4 title-readable">
                      {step.title}
                    </h3>
                    
                    <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                      {step.description}
                    </p>

                    <div className="bg-muted rounded-lg p-4 mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-2 h-2 bg-accent rounded-full"></div>
                        <span className="font-semibold title-readable">Duration: {step.duration}</span>
                      </div>
                      <ul className="space-y-2">
                        {step.details.map((detail, detailIndex) => (
                          <li key={detailIndex} className="flex items-center gap-2 text-sm">
                            <div className="w-1 h-1 bg-muted-foreground rounded-full"></div>
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Image */}
                  <div className={`${index % 2 === 1 ? 'lg:order-1' : ''} scroll-reveal-right stagger-${index + 2}`}>
                    <div className="relative rounded-xl overflow-hidden shadow-lg">
                      <img 
                        src={
                          index === 0
                            ? "/images/Image5.png"
                            : index === 1
                            ? "/images/Image7.png"
                            : "/images/image1.jpg"
                        }
                        alt={step.title}
                        className="w-full h-[300px] object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                    </div>
                  </div>
                </div>

                {/* Arrow */}
                {!isLast && (
                  <div className="flex justify-center mb-12">
                    <div className="flex items-center justify-center w-12 h-12 bg-accent/10 rounded-full">
                      <ArrowRight className="w-6 h-6 text-accent" />
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <div className="bg-muted rounded-2xl p-8">
            <h3 className="text-2xl font-bold mb-4 title-readable">
              Ready to Get Started?
            </h3>
            <p className="text-lg text-muted-foreground mb-6">
              Schedule your free consultation today and see how we can transform your floors.
            </p>
            <Button 
              size="lg"
              className="btn-hero"
              onClick={scrollToContact}
            >
              <Calendar className="w-5 h-5 mr-2" />
              Schedule Free Consultation
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Process;
