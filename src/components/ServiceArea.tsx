
import React from 'react';
import { MapPin, Clock, Car, Phone, Mail } from 'lucide-react';
import { trackPhoneClick } from '@/lib/analytics';

const ServiceArea = () => {
  const areas = [
    "Denver", "Aurora", "Lakewood", "Thornton", "Westminster", "Arvada",
    "Centennial", "Boulder", "Broomfield", "Commerce City", "Highlands Ranch",
    "Wheat Ridge", "Englewood", "Littleton", "Greenwood Village", "Parker"
  ];

  return (
    <section className="bg-background section-padding">
      <div className="container mx-auto container-padding">
        <div className="section-header scroll-reveal">
          <h2 className="section-title">
            We Serve the Greater Denver Area
          </h2>
          <p className="section-description">
            Professional wood floor refinishing and installation throughout Denver and surrounding communities.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Service Areas List */}
          <div>
            <h3 className="text-2xl font-bold mb-6 title-readable">
              Cities We Serve
            </h3>
            
            <div className="grid md:grid-cols-2 gap-3 mb-8">
              {areas.map((area, index) => (
                <div key={index} className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-accent" />
                  <span>{area}</span>
                </div>
              ))}
            </div>

            {/* Service Details */}
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Car className="w-5 h-5 text-accent mt-1" />
                <div>
                  <h4 className="font-semibold title-readable mb-1">Free Travel</h4>
                  <p className="text-muted-foreground text-sm">
                    No additional charges for travel within our service area
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Clock className="w-5 h-5 text-accent mt-1" />
                <div>
                  <h4 className="font-semibold title-readable mb-1">Same-Day Service</h4>
                  <p className="text-muted-foreground text-sm">
                    Free consultations and estimates available same day
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Map Placeholder */}
          <div className="relative">
            <div className="bg-muted rounded-xl h-[400px] flex items-center justify-center relative overflow-hidden">
              {/* Map Image */}
              <img 
                src="https://images.unsplash.com/photo-1524661135-423995f22d0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Denver area map showing our service area"
                className="w-full h-full object-cover"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-primary/80 flex items-center justify-center">
                <div className="text-center text-primary-foreground">
                  <MapPin className="w-16 h-16 mx-auto mb-4 text-accent" />
                  <h3 className="text-2xl font-bold mb-2">Greater Denver Area</h3>
                  <p className="text-lg opacity-90">Serving 15+ Communities</p>
                  <div className="mt-4 inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                    <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
                    <span className="text-sm">Service Area Coverage</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-white/95 backdrop-blur-sm rounded-lg p-4">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-2">
                    Call for service outside our main area
                  </p>
                  <a
                    href="tel:+17206019464"
                    className="font-bold title-readable hover:text-accent transition-colors"
                    onClick={() => trackPhoneClick('(*************', 'service-area')}
                  >
                    (*************
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Service Guarantee */}
        <div className="mt-16 text-center bg-muted rounded-2xl p-8">
          <h3 className="text-2xl font-bold mb-4 title-readable">
            Not Sure If We Serve Your Area?
          </h3>
          <p className="text-lg text-muted-foreground mb-6">
            We're always expanding our service area. Give us a call and we'll let you know if we can help with your wood flooring project.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="tel:+17206019464"
              className="inline-flex items-center justify-center btn-primary"
              onClick={() => trackPhoneClick('(*************', 'service-area-cta')}
            >
              <Phone className="w-5 h-5 mr-2" />
              Call (*************
            </a>
            {/* <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center justify-center btn-trust"
            >
              <Mail className="w-5 h-5 mr-2" />
              Email Us
            </a> */}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServiceArea;
