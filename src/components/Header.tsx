
import React, { useState } from 'react';
import { Phone, Menu, X, Award, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { trackPhoneClick } from '@/lib/analytics';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: 'smooth' });
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Sticky Top Bar */}
      <div className="sticky top-0 z-50 bg-primary text-primary-foreground py-2">
        <div className="container mx-auto container-padding flex justify-between items-center">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              <a
                href="tel:+17206019464"
                className="font-semibold hover:text-accent transition-colors"
                onClick={() => trackPhoneClick('(*************', 'header')}
              >
                (*************
              </a>
            </div>
            <div className="hidden md:flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Award className="w-4 h-4 text-accent" />
                <span>17 Years Experience 🏆</span>
              </div>
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 text-accent" />
                <span>Premium Quality 🏅</span>
              </div>
            </div>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            className="bg-accent text-accent-foreground border-accent hover:bg-accent/90"
            onClick={() => scrollToSection('contact')}
          >
            Free Estimate
          </Button>
        </div>
      </div>

      {/* Main Header */}
      <header className="bg-background border-b border-border shadow-sm">
        <div className="container mx-auto container-padding py-4">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <div className="flex items-center">
              <img
                src="/LOGO.PNG"
                alt="B&A Wood Floors Logo"
                className="h-12 md:h-16 w-auto mr-3"
              />
              <div>
                <h1 className="text-2xl md:text-3xl font-bold title-readable">
                  B&A Wood Floors
                </h1>
                <div className="hidden lg:block text-sm text-muted-foreground">
                  Sand • Refinish • Install • Pre-finished Floors
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <button 
                onClick={() => scrollToSection('services')}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Services
              </button>

              <button 
                onClick={() => scrollToSection('about')}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                About
              </button>
              <button 
                onClick={() => scrollToSection('testimonials')}
                className="text-foreground hover:text-primary transition-colors font-medium"
              >
                Reviews
              </button>
              <Button 
                className="btn-hero"
                onClick={() => scrollToSection('contact')}
              >
                Get Free Consultation
              </Button>
            </nav>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <nav className="md:hidden mt-4 pb-4 border-t border-border pt-4">
              <div className="flex flex-col space-y-4">
                <div className="text-sm text-muted-foreground mb-2">
                  Sand • Refinish • Install • Pre-finished Floors
                </div>
                <button 
                  onClick={() => scrollToSection('services')}
                  className="text-left text-foreground hover:text-primary transition-colors font-medium"
                >
                  Services
                </button>

                <button 
                  onClick={() => scrollToSection('about')}
                  className="text-left text-foreground hover:text-primary transition-colors font-medium"
                >
                  About
                </button>
                <button 
                  onClick={() => scrollToSection('testimonials')}
                  className="text-left text-foreground hover:text-primary transition-colors font-medium"
                >
                  Reviews
                </button>
                <Button 
                  className="btn-hero w-full"
                  onClick={() => scrollToSection('contact')}
                >
                  Get Free Consultation
                </Button>
              </div>
            </nav>
          )}
        </div>
      </header>
    </>
  );
};

export default Header;
