
import React, { useState } from 'react';
import { Chevron<PERSON><PERSON><PERSON>, ChevronRight, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';

const BeforeAfterGallery = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const projects = [
    {
      id: 1,
      title: "Cherry Hill Living Room",
      location: "Denver, CO",
      before: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60",
      after: "https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Complete refinishing brought this 1950s oak flooring back to life"
    },
    {
      id: 2,
      title: "Highlands Ranch Kitchen",
      location: "Highlands Ranch, CO",
      before: "https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60",
      after: "https://images.unsplash.com/photo-1582037928769-181f2644ecb7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Water damage restoration and refinishing transformed this kitchen"
    },
    {
      id: 3,
      title: "Lakewood Bedroom Suite",
      location: "Lakewood, CO",
      before: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60",
      after: "https://images.unsplash.com/photo-1560448204-603b3fc33ddc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Pet damage repair and refinishing created a stunning master suite"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % projects.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + projects.length) % projects.length);
  };

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="gallery" className="bg-muted section-padding">
      <div className="container mx-auto container-padding">
        {/* Header */}
        <div className="section-header scroll-reveal">
          <h2 className="section-title">
            See the Transformation
          </h2>
          <p className="section-description">
            Real Denver homes, real transformations. From worn and damaged to stunning and valuable.
          </p>
        </div>

        {/* Before/After Slider */}
        <div className="relative max-w-6xl mx-auto scroll-reveal-scale">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            {/* Before Image */}
            <div className="relative">
              <div className="absolute top-4 left-4 z-10">
                <span className="bg-destructive text-destructive-foreground px-3 py-1 rounded-full font-bold text-sm">
                  BEFORE
                </span>
              </div>
              <img 
                src={projects[currentSlide].before}
                alt={`Before refinishing - ${projects[currentSlide].title}`}
                className="w-full h-[400px] object-cover rounded-xl shadow-lg"
              />
            </div>

            {/* After Image */}
            <div className="relative">
              <div className="absolute top-4 left-4 z-10">
                <span className="bg-success text-success-foreground px-3 py-1 rounded-full font-bold text-sm">
                  AFTER
                </span>
              </div>
              <img 
                src={projects[currentSlide].after}
                alt={`After refinishing - ${projects[currentSlide].title}`}
                className="w-full h-[400px] object-cover rounded-xl shadow-lg"
              />
            </div>
          </div>

          {/* Project Info */}
          <div className="text-center mt-8">
            <h3 className="text-2xl font-bold title-readable mb-2">
              {projects[currentSlide].title}
            </h3>
            <p className="text-muted-foreground mb-2">
              {projects[currentSlide].location}
            </p>
            <p className="text-lg max-w-2xl mx-auto">
              {projects[currentSlide].description}
            </p>
          </div>

          {/* Navigation */}
          <div className="flex justify-center items-center gap-4 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={prevSlide}
              className="rounded-full w-10 h-10 p-0"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>

            {/* Dots */}
            <div className="flex gap-2">
              {projects.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentSlide ? 'bg-primary' : 'bg-muted-foreground/30'
                  }`}
                />
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={nextSlide}
              className="rounded-full w-10 h-10 p-0"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          {/* CTA */}
          <div className="text-center mt-8">
            <Button 
              size="lg"
              className="btn-hero"
              onClick={scrollToContact}
            >
              <Eye className="w-5 h-5 mr-2" />
              See What We Can Do For You
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BeforeAfterGallery;
