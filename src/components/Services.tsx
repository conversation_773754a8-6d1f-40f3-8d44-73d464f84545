
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ArrowR<PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useScrollReveal } from '@/hooks/useScrollReveal';
import { trackButtonClick, trackServiceInterest } from '@/lib/analytics';

const Services = () => {
  const sectionRef = useScrollReveal();
  const areaRef = useScrollReveal();

  const services = [
    {
      icon: Sparkles,
      title: "Wood Floor Refinishing",
      description: "Restore your existing hardwood floors to like-new condition. Sand, stain, and seal for a beautiful finish.",
      features: [
        "Complete sanding and refinishing",
        "Custom stain color matching",
        "Multiple finish options",
        "Dustless sanding system",
        "$3-$4 per sq ft"
      ],
      image: "/images/Image2.png",
      popular: true
    },
    {
      icon: Hammer,
      title: "Hardwood Floor Installation",
      description: "Professional installation of new hardwood floors with premium materials and expert craftsmanship.",
      features: [
        "Solid and engineered hardwood",
        "Multiple wood species available",
        "Professional subfloor preparation",
        "Precision installation",
        "Competitive pricing"
      ],
      image: "/images/Image3.png",
      popular: false
    },
    {
      icon: Wrench,
      title: "Floor Repairs & Restoration",
      description: "Fix water damage, scratches, gaps, and other issues to restore your floors to perfect condition.",
      features: [
        "Water damage restoration",
        "Scratch and gouge repair",
        "Gap filling and leveling",
        "Board replacement",
        "Pet damage repair"
      ],
      image: "/images/Image4.png",
      popular: false
    }
  ];

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="services" className="bg-muted section-padding rounded-t-4xl rounded-b-4xl">
      <div className="container mx-auto container-padding">
        <div
          ref={sectionRef}
          className="section-header scroll-reveal"
        >
          <h2 className="section-title">
            Our Services
          </h2>
          <p className="section-description">
            From refinishing to installation, we provide comprehensive wood flooring services
            for Denver area homeowners.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <div 
                key={index}
                className={`card-premium relative overflow-hidden group hover:shadow-xl transition-all duration-300 scroll-reveal stagger-${index + 1} ${service.popular ? 'ring-2 ring-accent' : ''}`}
              >
                {service.popular && (
                  <div className="absolute top-4 right-4">
                    <span className="special-badge text-xs">
                      MOST POPULAR
                    </span>
                  </div>
                )}

                <div className="relative h-48 mb-6 rounded-2xl overflow-hidden">
                  <img 
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                  <div className="absolute bottom-4 left-4">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-white/90 rounded-full">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-2xl font-bold mb-4 title-readable">
                    {service.title}
                  </h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-accent rounded-full"></div>
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    className="w-full btn-primary rounded-2xl"
                    onClick={() => {
                      trackButtonClick('Get Free Estimate', 'services', 'service-cta');
                      trackServiceInterest(service.title, 'inquiry');
                      scrollToContact();
                    }}
                  >
                    Get Free Estimate
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </div>
            );
          })}
        </div>

        {/* Service Area Note */}
        <div
          ref={areaRef}
          className="text-center mt-16 p-10 bg-background rounded-3xl border border-border scroll-reveal-scale"
        >
          <h3 className="text-3xl font-bold mb-6 title-readable">
            Serving the Greater Denver Area
          </h3>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            We proudly serve Denver, Aurora, Lakewood, Thornton, Westminster, Arvada,
            Centennial, Boulder, Broomfield, Commerce City, Highlands Ranch, and surrounding areas.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Services;
