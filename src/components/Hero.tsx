
import React from 'react';
import { Star, Shield, Award, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useScrollReveal } from '@/hooks/useScrollReveal';
import { trackPhoneClick, trackButtonClick } from '@/lib/analytics';

const Hero = () => {
  const heroRef = useScrollReveal();

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section className="relative hero-gradient wood-overlay min-h-[90vh] flex items-center">
      {/* Dark overlay for better text contrast */}
      <div className="absolute inset-0 bg-black/50"></div>
      <div className="container mx-auto container-padding relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div
            ref={heroRef}
            className="text-white scroll-reveal-left"
          >
            {/* Trust Badges */}
            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 border border-white/30">
                <Shield className="w-4 h-4 text-accent" />
                <span className="text-sm font-medium text-white">Licensed & Insured</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 border border-white/30">
                <Award className="w-4 h-4 text-accent" />
                <span className="text-sm font-medium text-white">17+ Years Experience</span>
              </div>
              <div className="flex items-center gap-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 border border-white/30">
                <Star className="w-4 h-4 text-accent" />
                <span className="text-sm font-medium text-white">Premium Quality</span>
              </div>
            </div>

            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Transform Your
              <span className="text-gradient block">Wood Floors</span>
            </h1>
            
            <p className="text-xl md:text-2xl mb-8 text-white/95 leading-relaxed">
              Professional wood floor refinishing and installation in Denver.
              <strong className="text-accent"> $3-$4 per sq ft</strong> –
              Sand/Refinish/Install/Pre-finished floors.
            </p>

            {/* Key Benefits */}
            <div className="grid md:grid-cols-2 gap-4 mb-8">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="font-medium text-white">Free In-Home Consultation</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="font-medium text-white">Same-Day Estimates</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="font-medium text-white">Licensed & Insured</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="font-medium text-white">17+ Years Experience</span>
              </div>
            </div>

            {/* Phone Number */}
            <div className="mb-6 p-4 bg-white/20 backdrop-blur-sm rounded-3xl border border-white/30">
              <div className="flex items-center justify-center gap-3">
                <Phone className="w-6 h-6 text-accent" />
                <div className="text-center">
                  <div className="text-sm text-white/90">Call or visit us online for a free estimate</div>
                  <a
                    href="tel:+17206019464"
                    className="text-2xl font-bold text-accent hover:text-accent/80 transition-colors"
                    onClick={() => trackPhoneClick('(*************', 'hero')}
                  >
                    (*************
                  </a>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="btn-hero text-lg px-8 py-4 rounded-3xl"
                onClick={() => {
                  trackButtonClick('Get My Free Consultation', 'hero', 'primary-cta');
                  scrollToContact();
                }}
              >
                Get My Free Consultation
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="bg-white/20 border-white/40 text-white hover:bg-white/30 backdrop-blur-sm text-lg px-8 py-4 rounded-3xl"
                onClick={() => {
                  trackButtonClick('View Our Services', 'hero', 'secondary-cta');
                  const element = document.getElementById('services');
                  element?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                View Our Services
              </Button>
            </div>
          </div>

          {/* Hero Image */}
          <div className="scroll-reveal-right">
            <div className="relative rounded-3xl overflow-hidden shadow-2xl">
              <img
                src="/images/Image5.png"
                alt="Beautiful refinished hardwood floors in Denver home"
                className="w-full h-[500px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              
              {/* Floating Stats */}
              <div className="absolute bottom-6 left-6 right-6">
                <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold title-readable">500+</div>
                      <div className="text-sm text-muted-foreground">Projects</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold title-readable">17+</div>
                      <div className="text-sm text-muted-foreground">Years</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold title-readable">5★</div>
                      <div className="text-sm text-muted-foreground">Rating</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
