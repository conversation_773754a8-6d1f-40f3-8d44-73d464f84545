
import React from 'react';
import { Phone, Mail, MapPin, Clock, Star, Facebook, Instagram, Shield, Award } from 'lucide-react';
import { trackPhoneClick } from '@/lib/analytics';
import { Link } from 'react-router-dom';

const Footer = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <footer className="bg-primary text-primary-foreground">
      {/* Main Footer */}
      <div className="container mx-auto container-padding py-12">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <h3 className="text-2xl font-bold mb-4">B&A Wood Floors</h3>
            <p className="text-primary-foreground/80 mb-6 leading-relaxed">
              Denver's trusted wood flooring specialists for over 20 years. We transform homes 
              with professional refinishing and installation services that add beauty and value.
            </p>

            {/* Trust Badges */}
            <div className="flex flex-wrap gap-4 mb-6">
              <div className="flex items-center gap-2 bg-white/10 rounded-full px-3 py-1 text-sm">
                <Shield className="w-4 h-4 text-accent" />
                <span>Licensed & Insured</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 rounded-full px-3 py-1 text-sm">
                <Star className="w-4 h-4 text-accent" />
                <span>5-Star Reviews</span>
              </div>
              <div className="flex items-center gap-2 bg-white/10 rounded-full px-3 py-1 text-sm">
                <Award className="w-4 h-4 text-accent" />
                <span>Locally Owned</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex gap-4">
              <a 
                href="#" 
                className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-accent/20 transition-colors"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center hover:bg-accent/20 transition-colors"
              >
                <Instagram className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <button 
                  onClick={() => scrollToSection('services')}
                  className="text-primary-foreground/80 hover:text-accent transition-colors"
                >
                  Our Services
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('gallery')}
                  className="text-primary-foreground/80 hover:text-accent transition-colors"
                >
                  Before & After Gallery
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection('testimonials')}
                  className="text-primary-foreground/80 hover:text-accent transition-colors"
                >
                  Customer Reviews
                </button>
              </li>
              <li>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="text-primary-foreground/80 hover:text-accent transition-colors"
                >
                  Free Consultation
                </button>
              </li>
              <li>
                <Link
                  to="/privacy-policy"
                  className="text-primary-foreground/80 hover:text-accent transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Us</h4>
            <div className="space-y-3">
              <a
                href="tel:+17206019464"
                className="flex items-center gap-3 text-primary-foreground/80 hover:text-accent transition-colors"
                onClick={() => trackPhoneClick('(*************', 'footer')}
              >
                <Phone className="w-4 h-4" />
                <span>(*************</span>
              </a>
              
              {/* <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-3 text-primary-foreground/80 hover:text-accent transition-colors"
              >
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </a> */}
              
              <div className="flex items-start gap-3 text-primary-foreground/80">
                <MapPin className="w-4 h-4 mt-1" />
                <span>Serving Greater Denver Area<br />Colorado</span>
              </div>
              
              <div className="flex items-start gap-3 text-primary-foreground/80">
                <Clock className="w-4 h-4 mt-1" />
                <div>
                  <div>Mon-Fri: 7AM-6PM</div>
                  <div>Sat: 8AM-4PM</div>
                  <div>Sun: Emergency only</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-white/10">
        <div className="container mx-auto container-padding py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-primary-foreground/60">
              © 2024 B&A Wood Floors. All rights reserved.
            </div>
            
            <div className="flex gap-6 text-sm">
              <Link to="/privacy-policy" className="text-primary-foreground/60 hover:text-accent transition-colors">
                Privacy Policy
              </Link>
              <a href="#" className="text-primary-foreground/60 hover:text-accent transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-primary-foreground/60 hover:text-accent transition-colors">
                License Info
              </a>
            </div>
            
            <div className="text-sm text-primary-foreground/60">
              Denver Wood Floor Refinishing & Installation
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
