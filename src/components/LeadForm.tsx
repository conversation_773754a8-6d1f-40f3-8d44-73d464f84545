
import React, { useState } from 'react';
import { Phone, Mail, Send, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { trackPhoneClick, trackFormSubmission, trackConversion } from '@/lib/analytics';

const LeadForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    projectDetails: '',
    address: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Track form submission attempt
      trackFormSubmission('consultation-request', formData);

      // Prepare form data for Netlify
      const form = e.target as HTMLFormElement;
      const netlifyFormData = new FormData(form);

      // Submit to Netlify
      const response = await fetch('/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(netlifyFormData as any).toString(),
      });

      if (response.ok) {
        // Track successful conversion
        trackConversion('consultation_request', 10);

        // Redirect to success page
        window.location.href = '/form-success';
      } else {
        throw new Error('Form submission failed');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Submission Error",
        description: "Sorry, there was an error. Please call us directly at (*************.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="bg-muted section-padding">
      <div className="container mx-auto container-padding">
        <div className="section-header scroll-reveal">
          <h2 className="section-title">
            Get Your Free Consultation
          </h2>
          <p className="section-description">
            Ready to transform your floors? Fill out the form below and we'll contact you within 2 hours
            to schedule your free in-home consultation.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="card-premium p-8 scroll-reveal-left">
              <div className="mb-6">
                <div className="special-badge inline-block mb-4">
                  FREE CONSULTATION
                </div>
                <h3 className="text-2xl font-bold title-readable mb-2">
                  Schedule Your Visit
                </h3>
                <p className="text-muted-foreground">
                  Get a detailed assessment and transparent pricing for your project.
                </p>
              </div>

              <form
                onSubmit={handleSubmit}
                className="space-y-6"
                name="consultation-request"
                method="POST"
                data-netlify="true"
                netlify-honeypot="bot-field"
                action="/form-success"
              >
                {/* Hidden fields for Netlify Forms */}
                <input type="hidden" name="form-name" value="consultation-request" />
                <div style={{ display: 'none' }}>
                  <label>
                    Don't fill this out if you're human: <input name="bot-field" />
                  </label>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium title-readable mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="John Smith"
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium title-readable mb-2">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      required
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="(*************"
                    />
                  </div>
                </div>

                {/* <div>
                  <label htmlFor="email" className="block text-sm font-medium title-readable mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="<EMAIL>"
                  />
                </div> */}

                {/* <div>
                  <label htmlFor="address" className="block text-sm font-medium title-readable mb-2">
                    Project Address
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="123 Main St, Denver, CO 80202"
                  />
                </div> */}

                <div>
                  <label htmlFor="service" className="block text-sm font-medium title-readable mb-2">
                    Service Needed *
                  </label>
                  <select
                    id="service"
                    name="service"
                    required
                    value={formData.service}
                    onChange={handleInputChange}
                    className="form-input"
                  >
                    <option value="">Select a service...</option>
                    <option value="refinishing">Wood Floor Refinishing</option>
                    <option value="installation">Hardwood Installation</option>
                    <option value="repair">Floor Repair & Restoration</option>
                    <option value="consultation">Just Need Consultation</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="projectDetails" className="block text-sm font-medium title-readable mb-2">
                    Project Details
                  </label>
                  <textarea
                    id="projectDetails"
                    name="projectDetails"
                    rows={4}
                    value={formData.projectDetails}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="Tell us about your project: room size, current floor condition, timeline, specific concerns, etc."
                  />
                </div>

                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="w-full btn-hero text-lg py-4"
                >
                  {isSubmitting ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-current mr-2"></div>
                      Submitting...
                    </div>
                  ) : (
                    <>
                      <Send className="w-5 h-5 mr-2" />
                      Get My Free Consultation
                    </>
                  )}
                </Button>

                <p className="text-sm text-muted-foreground text-center">
                  * We'll contact you within 2 hours to schedule your free consultation
                </p>
              </form>
            </div>

            {/* Contact Info & Benefits */}
            <div className="space-y-8 scroll-reveal-right">
              {/* Contact Methods */}
              <div className="card-premium p-8">
                <h3 className="text-2xl font-bold title-readable mb-6">
                  Prefer to Call?
                </h3>
                
                <div className="space-y-4">
                  <a
                    href="tel:+17206019464"
                    className="flex items-center gap-4 p-4 bg-muted rounded-lg hover:bg-accent/10 transition-colors group"
                    onClick={() => trackPhoneClick('(*************', 'contact-form')}
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-accent/10 rounded-full group-hover:bg-accent/20 transition-colors">
                      <Phone className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <div className="font-bold title-readable">(*************</div>
                      <div className="text-sm text-muted-foreground">Call for immediate assistance</div>
                    </div>
                  </a>

                  {/* <a
                    href="mailto:<EMAIL>"
                    className="flex items-center gap-4 p-4 bg-muted rounded-lg hover:bg-accent/10 transition-colors group"
                  >
                    <div className="flex items-center justify-center w-12 h-12 bg-accent/10 rounded-full group-hover:bg-accent/20 transition-colors">
                      <Mail className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <div className="font-bold title-readable"><EMAIL></div>
                      <div className="text-sm text-muted-foreground">Email us your questions</div>
                    </div>
                  </a> */}
                </div>

                <div className="mt-6 p-4 bg-trust/10 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <CheckCircle className="w-5 h-5 text-trust" />
                    <span className="font-semibold text-trust">Business Hours</span>
                  </div>
                  <div className="text-sm space-y-1">
                    <div>Monday - Friday: 7:00 AM - 6:00 PM</div>
                    <div>Saturday: 8:00 AM - 4:00 PM</div>
                    <div>Sunday: Emergency calls only</div>
                  </div>
                </div>
              </div>

              {/* What to Expect */}
              <div className="card-premium p-8">
                <h3 className="text-2xl font-bold title-readable mb-6">
                  What to Expect
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-accent/10 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-accent rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold title-readable">Quick Response</h4>
                      <p className="text-sm text-muted-foreground">We'll contact you within 2 hours during business hours</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-accent/10 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-accent rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold title-readable">Free Assessment</h4>
                      <p className="text-sm text-muted-foreground">Complete floor evaluation with no obligations</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-accent/10 rounded-full flex items-center justify-center mt-1">
                      <div className="w-2 h-2 bg-accent rounded-full"></div>
                    </div>
                    <div>
                      <h4 className="font-semibold title-readable">Transparent Pricing</h4>
                      <p className="text-sm text-muted-foreground">Detailed estimate with no hidden fees</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LeadForm;
