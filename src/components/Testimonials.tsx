import React, { useState } from 'react';
import { Star, ChevronLeft, ChevronRight, Quote } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useScrollReveal } from '@/hooks/useScrollReveal';

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const sectionRef = useScrollReveal();
  const ctaRef = useScrollReveal();

  const testimonials = [
    {
      name: "<PERSON> & <PERSON>",
      location: "Cherry Hills Village, CO",
      rating: 5,
      text: "B&A Wood Floors transformed our 20-year-old oak floors into something absolutely stunning. The team was professional, clean, and the results exceeded our expectations. Our home value increased significantly!",
      project: "Living room & kitchen refinishing",
      image: "/images/Image2.png"
    },
    {
      name: "<PERSON>",
      location: "Highlands Ranch, CO",
      rating: 5,
      text: "After water damage in our kitchen, we thought we'd need to replace everything. B&A saved our beautiful hardwood floors and made them look brand new. The $250 August special made it even better!",
      project: "Water damage restoration",
      image: "/images/Image3.png"
    },
    {
      name: "<PERSON>",
      location: "Boulder, CO",
      rating: 5,
      text: "The free consultation was incredibly helpful. They explained everything clearly, worked within our budget, and delivered exactly what they promised. The dustless sanding was a game-changer with kids in the house.",
      project: "Whole house refinishing",
      image: "/images/Image4.png"
    },
    {
      name: "Robert & Linda Thompson",
      location: "Lakewood, CO",
      rating: 5,
      text: "We've lived in Denver for 30 years and B&A is by far the best flooring company we've worked with. Professional, reliable, and the craftsmanship is outstanding. Highly recommend!",
      project: "Staircase & hallway refinishing",
      image: "/images/Image5.png"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="testimonials" className="bg-muted section-padding rounded-t-4xl rounded-b-4xl">
      <div className="container mx-auto container-padding">
        <div
          ref={sectionRef}
          className="section-header scroll-reveal"
        >
          <h2 className="section-title">
            What Our Customers Say
          </h2>
          <p className="section-description">
            Don't just take our word for it. Here's what Denver homeowners are saying about their experience with B&A Wood Floors.
          </p>
        </div>

        {/* Main Testimonial */}
        <div className="max-w-4xl mx-auto mb-12 scroll-reveal-scale">
          <div className="card-testimonial relative">
            <Quote className="absolute top-4 right-4 w-8 h-8 text-accent/20" />
            
            <div className="flex items-center gap-4 mb-6">
              <img 
                src={testimonials[currentTestimonial].image}
                alt={testimonials[currentTestimonial].name}
                className="w-16 h-16 rounded-full object-cover"
              />
              <div>
                <h3 className="font-bold text-lg title-readable">
                  {testimonials[currentTestimonial].name}
                </h3>
                <p className="text-muted-foreground">
                  {testimonials[currentTestimonial].location}
                </p>
                <div className="flex items-center gap-1 mt-1">
                  {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-accent text-accent" />
                  ))}
                </div>
              </div>
            </div>

            <blockquote className="text-lg leading-relaxed text-foreground mb-4">
              "{testimonials[currentTestimonial].text}"
            </blockquote>

            <div className="text-sm text-muted-foreground">
              <strong>Project:</strong> {testimonials[currentTestimonial].project}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-center items-center gap-4 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={prevTestimonial}
              className="rounded-full w-10 h-10 p-0"
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>

            {/* Dots */}
            <div className="flex gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentTestimonial ? 'bg-primary' : 'bg-muted-foreground/30'
                  }`}
                />
              ))}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={nextTestimonial}
              className="rounded-full w-10 h-10 p-0"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-12">
          <div className="text-center scroll-reveal stagger-1">
            <div className="text-3xl font-bold title-readable mb-2">500+</div>
            <div className="text-muted-foreground">Happy Customers</div>
          </div>
          <div className="text-center scroll-reveal stagger-2">
            <div className="text-3xl font-bold title-readable mb-2">5.0</div>
            <div className="text-muted-foreground">Average Rating</div>
          </div>
          <div className="text-center scroll-reveal stagger-3">
            <div className="text-3xl font-bold title-readable mb-2">17+</div>
            <div className="text-muted-foreground">Years Experience</div>
          </div>
          <div className="text-center scroll-reveal stagger-4">
            <div className="text-3xl font-bold title-readable mb-2">100%</div>
            <div className="text-muted-foreground">Satisfaction</div>
          </div>
        </div>

        {/* CTA */}
        <div 
          ref={ctaRef}
          className="text-center scroll-reveal-scale"
        >
          <div className="bg-background rounded-3xl p-8 border border-border">
            <h3 className="text-2xl font-bold mb-4 title-readable">
              Join Our Happy Customers
            </h3>
            <p className="text-lg text-muted-foreground mb-6">
              Ready to transform your floors? Get your free consultation and see why Denver homeowners choose B&A Wood Floors.
            </p>
            <Button 
              size="lg"
              className="btn-hero rounded-3xl"
              onClick={scrollToContact}
            >
              Get My Free Consultation
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
