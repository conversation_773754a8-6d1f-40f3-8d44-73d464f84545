
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/*
 * Color constants are defined in src/lib/colors.ts
 * This CSS file uses the color values from that centralized location
 */

@layer base {
  :root {
    /* Updated with lighter brown palette */
    --background: 45 30% 98%;
    --foreground: 25 20% 25%;
    --card: 45 35% 99%;
    --card-foreground: 25 20% 25%;
    --popover: 45 35% 99%;
    --popover-foreground: 25 20% 25%;
    --primary: 30 25% 45%;
    --primary-foreground: 45 30% 98%;
    --secondary: 40 30% 95%;
    --secondary-foreground: 25 20% 25%;
    --muted: 40 25% 96%;
    --muted-foreground: 25 15% 55%;
    --accent: 45 80% 65%;
    --accent-foreground: 25 20% 25%;
    --trust: 120 45% 45%;
    --trust-foreground: 45 30% 98%;
    --destructive: 15 75% 55%;
    --destructive-foreground: 45 30% 98%;
    --success: 120 45% 45%;
    --success-foreground: 45 30% 98%;
    --border: 40 25% 92%;
    --input: 40 25% 92%;
    --ring: 45 80% 65%;
    --radius: 1rem;
    --wood-gradient: linear-gradient(135deg, hsl(30 25% 45%) 0%, hsl(35 35% 55%) 100%);
    --gold-gradient: linear-gradient(135deg, hsl(45 80% 65%) 0%, hsl(50 85% 75%) 100%);
    --hero-gradient: linear-gradient(135deg, hsl(25 35% 35%) 0%, hsl(30 40% 45%) 50%, hsl(35 45% 55%) 100%);
  }

  .dark {
    --background: 25 30% 8%;
    --foreground: 45 30% 95%;
    --card: 25 30% 10%;
    --card-foreground: 45 30% 95%;
    --popover: 25 30% 10%;
    --popover-foreground: 45 30% 95%;
    --primary: 45 30% 95%;
    --primary-foreground: 25 30% 15%;
    --secondary: 25 25% 20%;
    --secondary-foreground: 45 30% 95%;
    --muted: 25 25% 20%;
    --muted-foreground: 40 20% 70%;
    --accent: 45 80% 75%;
    --accent-foreground: 25 30% 15%;
    --trust: 120 45% 50%;
    --trust-foreground: 45 30% 95%;
    --destructive: 15 75% 55%;
    --destructive-foreground: 45 30% 95%;
    --success: 120 45% 50%;
    --success-foreground: 45 30% 95%;
    --border: 25 25% 25%;
    --input: 25 25% 25%;
    --ring: 45 80% 75%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-accent to-accent/80 bg-clip-text text-transparent;
  }

  .hero-gradient {
    background: var(--hero-gradient);
  }

  .wood-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  .btn-hero {
    @apply bg-accent hover:bg-accent/90 text-accent-foreground font-bold shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground font-semibold;
  }

  .btn-trust {
    @apply bg-trust hover:bg-trust/90 text-trust-foreground font-semibold;
  }

  .card-premium {
    @apply bg-card border border-border rounded-3xl shadow-sm hover:shadow-lg transition-all duration-300;
  }

  .form-input {
    @apply w-full px-4 py-3 border border-border rounded-xl bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent transition-all duration-200;
  }

  .card-testimonial {
    @apply bg-card border border-border rounded-3xl p-8 shadow-sm;
  }

  .special-badge {
    @apply bg-accent text-accent-foreground px-3 py-1 rounded-full font-bold;
  }

  .section-padding {
    @apply py-20 md:py-28;
  }

  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }

  .section-header {
    @apply text-center mb-16;
  }

  .section-title {
    @apply text-4xl md:text-5xl font-bold mb-6;
    color: #2f2f2f;
  }

  .section-description {
    @apply text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed;
  }

  /* Readable text color for white backgrounds */
  .text-readable {
    color: #2f2f2f !important;
  }

  .title-readable {
    color: #2f2f2f !important;
  }

  .subtitle-readable {
    color: #2f2f2f !important;
  }

  /* Enhanced Scroll animations */
  .scroll-reveal {
    opacity: 0;
    transform: translateY(60px);
    transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
  }

  .scroll-reveal-left {
    opacity: 0;
    transform: translateX(-60px);
    transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .scroll-reveal-left.revealed {
    opacity: 1;
    transform: translateX(0);
  }

  .scroll-reveal-right {
    opacity: 0;
    transform: translateX(60px);
    transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .scroll-reveal-right.revealed {
    opacity: 1;
    transform: translateX(0);
  }

  .scroll-reveal-scale {
    opacity: 0;
    transform: scale(0.85);
    transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .scroll-reveal-scale.revealed {
    opacity: 1;
    transform: scale(1);
  }

  .scroll-reveal-fade {
    opacity: 0;
    transition: all 1.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .scroll-reveal-fade.revealed {
    opacity: 1;
  }

  .scroll-reveal-rotate {
    opacity: 0;
    transform: rotate(-5deg) translateY(30px);
    transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .scroll-reveal-rotate.revealed {
    opacity: 1;
    transform: rotate(0deg) translateY(0);
  }

  /* Enhanced Staggered animations */
  .stagger-1 { transition-delay: 0.1s; }
  .stagger-2 { transition-delay: 0.2s; }
  .stagger-3 { transition-delay: 0.3s; }
  .stagger-4 { transition-delay: 0.4s; }
  .stagger-5 { transition-delay: 0.5s; }
  .stagger-6 { transition-delay: 0.6s; }
  .stagger-7 { transition-delay: 0.7s; }
  .stagger-8 { transition-delay: 0.8s; }

  /* Section-specific animations */
  .animate-section-enter {
    animation: sectionEnter 1.2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  @keyframes sectionEnter {
    0% {
      opacity: 0;
      transform: translateY(40px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Smooth section transitions */
  section {
    transition: all 0.3s ease-in-out;
  }
}
