/**
 * Centralized Color Constants for Denver Floor Glow
 * 
 * This file contains all color definitions used throughout the website.
 * Update colors here to maintain consistency across all components.
 */

// Base Colors (HSL format for CSS custom properties)
export const COLORS = {
  // Light Brown Palette - Warmer and lighter tones
  primary: {
    hue: 30,
    saturation: 25,
    lightness: 45, // Lighter brown
    hsl: '30 25% 45%',
    hex: '#8B6F47'
  },
  
  // Very Light Brown for backgrounds
  background: {
    hue: 45,
    saturation: 30,
    lightness: 98,
    hsl: '45 30% 98%',
    hex: '#FEFCFA'
  },
  
  // Card backgrounds - slightly warmer white
  card: {
    hue: 45,
    saturation: 35,
    lightness: 99,
    hsl: '45 35% 99%',
    hex: '#FFFEFB'
  },
  
  // Readable text color for white backgrounds
  text: {
    primary: '#2f2f2f', // More readable than pure black
    secondary: '#4a4a4a',
    muted: '#6b6b6b'
  },
  
  // Light brown for foreground text
  foreground: {
    hue: 25,
    saturation: 20,
    lightness: 25,
    hsl: '25 20% 25%',
    hex: '#453A32'
  },
  
  // Golden accent color - brighter and more vibrant
  accent: {
    hue: 45,
    saturation: 80,
    lightness: 65,
    hsl: '45 80% 65%',
    hex: '#E6C068'
  },
  
  // Secondary light brown
  secondary: {
    hue: 40,
    saturation: 30,
    lightness: 95,
    hsl: '40 30% 95%',
    hex: '#F7F4F0'
  },
  
  // Muted brown for subtle elements
  muted: {
    hue: 40,
    saturation: 25,
    lightness: 96,
    hsl: '40 25% 96%',
    hex: '#F8F6F3'
  },
  
  // Trust/success green
  trust: {
    hue: 120,
    saturation: 45,
    lightness: 45,
    hsl: '120 45% 45%',
    hex: '#4A8B4A'
  },
  
  // Borders - light brown
  border: {
    hue: 40,
    saturation: 25,
    lightness: 92,
    hsl: '40 25% 92%',
    hex: '#EDE8E2'
  },
  
  // Input fields
  input: {
    hue: 40,
    saturation: 25,
    lightness: 92,
    hsl: '40 25% 92%',
    hex: '#EDE8E2'
  }
} as const;

// Gradient definitions
export const GRADIENTS = {
  wood: `linear-gradient(135deg, hsl(${COLORS.primary.hsl}) 0%, hsl(35 35% 55%) 100%)`,
  gold: `linear-gradient(135deg, hsl(${COLORS.accent.hsl}) 0%, hsl(50 85% 75%) 100%)`,
  hero: `linear-gradient(135deg, hsl(25 35% 35%) 0%, hsl(30 40% 45%) 50%, hsl(35 45% 55%) 100%)`
} as const;

// Dark mode colors
export const DARK_COLORS = {
  background: {
    hue: 25,
    saturation: 30,
    lightness: 8,
    hsl: '25 30% 8%'
  },
  foreground: {
    hue: 45,
    saturation: 30,
    lightness: 95,
    hsl: '45 30% 95%'
  },
  card: {
    hue: 25,
    saturation: 30,
    lightness: 10,
    hsl: '25 30% 10%'
  },
  primary: {
    hue: 45,
    saturation: 30,
    lightness: 95,
    hsl: '45 30% 95%'
  },
  accent: {
    hue: 45,
    saturation: 80,
    lightness: 75,
    hsl: '45 80% 75%'
  },
  border: {
    hue: 25,
    saturation: 25,
    lightness: 25,
    hsl: '25 25% 25%'
  }
} as const;

// Utility function to convert HSL to CSS custom property format
export const hslToCustomProperty = (hsl: string) => hsl;

// Export individual color values for easy access
export const PRIMARY_COLOR = COLORS.primary.hsl;
export const ACCENT_COLOR = COLORS.accent.hsl;
export const BACKGROUND_COLOR = COLORS.background.hsl;
export const TEXT_PRIMARY = COLORS.text.primary;
export const TEXT_SECONDARY = COLORS.text.secondary;
export const TEXT_MUTED = COLORS.text.muted;
