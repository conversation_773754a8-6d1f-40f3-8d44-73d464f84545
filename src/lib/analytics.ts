// Google Analytics 4 Event Tracking Utilities

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: any) => void;
  }
}

// Check if gtag is available
const isGtagAvailable = (): boolean => {
  return typeof window !== 'undefined' && typeof window.gtag === 'function';
};

// Generic event tracking function
export const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (isGtagAvailable()) {
    window.gtag('event', eventName, parameters);
    console.log('Analytics Event:', eventName, parameters);
  } else {
    console.log('Analytics not available - Event would be:', eventName, parameters);
  }
};

// Phone number click tracking
export const trackPhoneClick = (phoneNumber: string, location: string) => {
  trackEvent('phone_click', {
    event_category: 'engagement',
    event_label: phoneNumber,
    phone_number: phoneNumber,
    click_location: location,
    value: 1
  });
};

// Form submission tracking
export const trackFormSubmission = (formName: string, formData: Record<string, any> = {}) => {
  trackEvent('form_submit', {
    event_category: 'conversion',
    event_label: formName,
    form_name: formName,
    service_type: formData.service || 'unknown',
    value: 10 // Assign value to form submissions
  });
};

// Button click tracking
export const trackButtonClick = (buttonText: string, buttonLocation: string, buttonType: string = 'cta') => {
  trackEvent('button_click', {
    event_category: 'engagement',
    event_label: buttonText,
    button_text: buttonText,
    button_location: buttonLocation,
    button_type: buttonType,
    value: 1
  });
};

// Page section view tracking
export const trackSectionView = (sectionName: string) => {
  trackEvent('section_view', {
    event_category: 'engagement',
    event_label: sectionName,
    section_name: sectionName
  });
};

// Conversion tracking (for high-value actions)
export const trackConversion = (conversionType: string, value: number = 0) => {
  trackEvent('conversion', {
    event_category: 'conversion',
    event_label: conversionType,
    conversion_type: conversionType,
    value: value
  });
};

// Service interest tracking
export const trackServiceInterest = (serviceName: string, action: string) => {
  trackEvent('service_interest', {
    event_category: 'engagement',
    event_label: serviceName,
    service_name: serviceName,
    action: action, // 'view', 'click', 'inquiry'
    value: 1
  });
};

// Special offer tracking
export const trackSpecialOfferInteraction = (offerName: string, action: string) => {
  trackEvent('special_offer', {
    event_category: 'promotion',
    event_label: offerName,
    offer_name: offerName,
    action: action, // 'view', 'click', 'claim'
    value: 1
  });
};

// Error tracking
export const trackError = (errorType: string, errorMessage: string) => {
  trackEvent('error', {
    event_category: 'error',
    event_label: errorType,
    error_type: errorType,
    error_message: errorMessage
  });
};
