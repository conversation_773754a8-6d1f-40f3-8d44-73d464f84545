
import { useEffect, useRef } from 'react';

export const useScrollReveal = () => {
  const revealRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('revealed');

            // Also reveal any child elements with scroll-reveal classes
            const childElements = entry.target.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right, .scroll-reveal-scale, .scroll-reveal-fade, .scroll-reveal-rotate');
            childElements.forEach((child, index) => {
              setTimeout(() => {
                child.classList.add('revealed');
              }, index * 100); // Stagger child animations
            });
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px',
      }
    );

    const element = revealRef.current;
    if (element) {
      observer.observe(element);

      // Also observe any child elements with scroll-reveal classes
      const childElements = element.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right, .scroll-reveal-scale, .scroll-reveal-fade, .scroll-reveal-rotate');
      childElements.forEach((child) => {
        observer.observe(child);
      });
    }

    return () => {
      if (element) {
        observer.unobserve(element);
        const childElements = element.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right, .scroll-reveal-scale, .scroll-reveal-fade, .scroll-reveal-rotate');
        childElements.forEach((child) => {
          observer.unobserve(child);
        });
      }
    };
  }, []);

  return revealRef;
};

// Global scroll reveal initialization for all elements
export const initGlobalScrollReveal = () => {
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('revealed');
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px',
      }
    );

    // Observe all scroll-reveal elements
    const elements = document.querySelectorAll('.scroll-reveal, .scroll-reveal-left, .scroll-reveal-right, .scroll-reveal-scale, .scroll-reveal-fade, .scroll-reveal-rotate');
    elements.forEach((element) => {
      observer.observe(element);
    });

    return () => {
      elements.forEach((element) => {
        observer.unobserve(element);
      });
    };
  }, []);
};
