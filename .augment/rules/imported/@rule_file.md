---
type: "manual"
---

1. Preservation of Features
Never remove existing features unless explicitly instructed.

If fixing a bug, make the smallest possible changes necessary to preserve existing functionality and minimize potential side effects.

2. Avoiding Duplicate Components
Thoroughly review the existing codebase before creating new components, functions, or files.

Modify existing components or functions to incorporate new functionality or bug fixes without duplication.

Identify and consolidate duplicate or overlapping code into a single, clear, and comprehensive implementation, explicitly handling all necessary cases.

3. Component and Function Reusability
Develop modular components and functions that can easily be reused across different parts of the project.

<PERSON><PERSON><PERSON> frequently used logic into shared utilities or helper functions.

Clearly document each reusable component or function, specifying its purpose, input, output, and usage examples.

4. Code Consistency and Style
Adhere consistently to established coding standards and patterns throughout the project.

Employ clear, concise, and descriptive naming conventions for variables, functions, components, and files.

Follow strict linting rules and enforce uniform formatting (indentation, spacing, imports/exports structure).

5. Simplicity and Clarity
Prioritize simplicity and clarity over complexity and cleverness.

Break down complex logic into smaller, simpler functions, each with a single clear responsibility.

Use straightforward control flows and minimize deeply nested conditionals or loops.

6. Edge Case Handling
Explicitly handle all known and potential edge cases when creating or modifying code.

Clearly comment within the code explaining why and how each edge case is handled.

Include tests that cover these edge cases comprehensively.

7. <PERSON><PERSON><PERSON> and Error Handling
Anticipate possible failure points and handle errors gracefully with clear and informative messages.

Avoid silent failures; instead, log errors and exceptions explicitly for easier debugging and maintenance.

8. Code Validation and Testing
Validate all code changes rigorously by running existing tests and developing new tests as needed.

Clearly document test cases and scenarios that validate correctness and prevent regressions.

Use test-driven development (TDD) principles where appropriate, creating tests prior to coding new functionality.

9. Documentation and Comments
Clearly comment significant code changes, explaining the purpose, context, and rationale.

Keep documentation accurate and up-to-date alongside code modifications.

Include concise inline comments only where necessary to clarify complex or non-obvious logic.

10. Performance and Optimization
Avoid premature optimization, but remain aware of potential performance bottlenecks.

Optimize only after profiling identifies genuine performance issues, and clearly document any optimization choices.

11. Explicit Instruction Compliance
Make code modifications strictly according to explicit instructions provided.

Proactively seek clarification whenever instructions are ambiguous or incomplete before implementation.

12. Maintainability
Favor readability and maintainability above all else, writing code that is easy for others (and future developers) to understand.

Regularly refactor and tidy up code as necessary to maintain clarity, consistency, and simplicity.

