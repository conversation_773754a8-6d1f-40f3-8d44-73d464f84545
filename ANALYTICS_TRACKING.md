# Analytics Tracking Implementation

## 📊 Overview

This website includes comprehensive Google Analytics 4 (GA4) tracking to monitor user interactions, conversions, and business performance. All phone number clicks, form submissions, and important button clicks are tracked.

## 🔧 Setup

### Google Analytics 4 Configuration
- **Measurement ID**: `G-LR53FJZ2BL`
- **Enhanced Measurements**: Enabled for scrolls, outbound clicks, and file downloads
- **Location**: `index.html` (lines 66-83)

### Analytics Utility Functions
- **Location**: `src/lib/analytics.ts`
- **Purpose**: Centralized tracking functions for consistent event reporting

## 📞 Phone Number Click Tracking

**Event Name**: `phone_click`

**Tracked Locations**:
- ✅ Header phone number
- ✅ Hero section phone number  
- ✅ Footer phone number
- ✅ Contact form phone number
- ✅ FAQ section phone number
- ✅ Service area phone numbers (2 locations)
- ✅ August special phone number

**Event Parameters**:
```javascript
{
  event_category: 'engagement',
  event_label: '(*************',
  phone_number: '(*************',
  click_location: 'header|hero|footer|contact-form|faq|service-area|august-special',
  value: 1
}
```

## 📝 Form Submission Tracking

**Event Name**: `form_submit`

**Tracked Forms**:
- ✅ Consultation request form (main contact form)

**Event Parameters**:
```javascript
{
  event_category: 'conversion',
  event_label: 'consultation-request',
  form_name: 'consultation-request',
  service_type: 'refinishing|installation|repair|consultation',
  value: 10
}
```

**Conversion Tracking**:
- **Event Name**: `conversion`
- **Triggered**: On successful form submission
- **Value**: 10 (assigned value for consultation requests)

## 🔘 Button Click Tracking

**Event Name**: `button_click`

**Tracked Buttons**:
- ✅ Hero CTA: "Get My Free Consultation" (primary)
- ✅ Hero CTA: "View Our Services" (secondary)
- ✅ Service cards: "Get Free Estimate" buttons

**Event Parameters**:
```javascript
{
  event_category: 'engagement',
  event_label: 'Button Text',
  button_text: 'Get My Free Consultation',
  button_location: 'hero|services',
  button_type: 'primary-cta|secondary-cta|service-cta',
  value: 1
}
```

## 🎯 Service Interest Tracking

**Event Name**: `service_interest`

**Triggered**: When users click service-specific buttons

**Event Parameters**:
```javascript
{
  event_category: 'engagement',
  event_label: 'Wood Floor Refinishing',
  service_name: 'Wood Floor Refinishing|New Floor Installation|Floor Repair & Restoration',
  action: 'inquiry',
  value: 1
}
```

## 🎉 Special Offer Tracking

**Event Name**: `special_offer`

**Tracked Offers**:
- ✅ August $250 OFF Special

**Event Parameters**:
```javascript
{
  event_category: 'promotion',
  event_label: 'August $250 OFF Special',
  offer_name: 'August $250 OFF Special',
  action: 'claim',
  value: 1
}
```

## 📈 Key Metrics to Monitor

### Conversion Metrics
1. **Phone Clicks**: Track which locations generate the most calls
2. **Form Submissions**: Monitor consultation request conversions
3. **Service Interest**: See which services are most popular
4. **Special Offers**: Track promotion effectiveness

### Engagement Metrics
1. **Button Clicks**: Monitor CTA performance
2. **Page Scrolling**: Automatic GA4 enhanced measurement
3. **Time on Page**: Standard GA4 metric
4. **Bounce Rate**: Standard GA4 metric

## 🔍 How to View Data in Google Analytics

### Real-Time Tracking
1. Go to GA4 → **Reports** → **Realtime**
2. Click on phone numbers or buttons on the website
3. See events appear in real-time under "Events"

### Custom Events
1. Go to GA4 → **Reports** → **Engagement** → **Events**
2. Look for custom events:
   - `phone_click`
   - `form_submit`
   - `button_click`
   - `service_interest`
   - `special_offer`
   - `conversion`

### Conversion Tracking
1. Go to GA4 → **Admin** → **Conversions**
2. Mark important events as conversions:
   - `phone_click` (high value)
   - `form_submit` (highest value)
   - `conversion` (already marked)

## 🚀 Deployment Notes

### For Netlify Deployment
- ✅ Analytics code is in `index.html` and will be included in build
- ✅ All tracking functions work client-side
- ✅ No server-side configuration needed

### Testing
- ✅ Events log to console in development
- ✅ Events send to GA4 in production
- ✅ Real-time testing available in GA4 dashboard

## 📊 Expected Results

After deployment, you'll be able to track:
- **Which phone numbers get clicked most** (header vs hero vs footer)
- **Form submission rates** and conversion funnel
- **Most popular services** based on button clicks
- **Special offer effectiveness**
- **User engagement patterns**

This data will help optimize the website for better conversions and understand customer behavior patterns.
