[build]
  # Build command
  command = "npm run build"
  
  # Directory to publish
  publish = "dist"
  
  # Environment variables
  [build.environment]
    NODE_VERSION = "18"

[form]
  # Form settings
  [form.settings]
    # Enable form detection
    autodetect = true

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Cache images
[[headers]]
  for = "/images/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
